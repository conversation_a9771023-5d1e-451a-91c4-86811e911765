import { Request, Response, NextFunction } from 'express';
import { createClient } from 'redis';

// Redis客户端配置
const redis = createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379'
});

// 连接Redis
redis.connect().catch(console.error);

// 登录保护配置
const LOGIN_PROTECTION_CONFIG = {
  maxAttempts: 5,           // 最大失败次数
  lockoutDuration: 30 * 60, // 锁定时长（秒）30分钟
  attemptWindow: 15 * 60,   // 尝试窗口（秒）15分钟
  keyPrefix: 'login_attempts:' // Redis键前缀
};

// 获取客户端IP地址
function getClientIP(req: Request): string {
  const forwarded = req.headers['x-forwarded-for'] as string;
  const ip = forwarded ? forwarded.split(',')[0].trim() : req.connection.remoteAddress || req.socket.remoteAddress;
  return ip || 'unknown';
}

// 获取登录尝试记录
async function getLoginAttempts(ip: string): Promise<{ count: number; lockedUntil?: number }> {
  try {
    const key = `${LOGIN_PROTECTION_CONFIG.keyPrefix}${ip}`;
    const data = await redis.get(key);
    
    if (!data) {
      return { count: 0 };
    }
    
    return JSON.parse(data);
  } catch (error) {
    console.error('获取登录尝试记录失败:', error);
    return { count: 0 };
  }
}

// 记录登录失败
async function recordLoginFailure(ip: string): Promise<{ count: number; lockedUntil?: number }> {
  try {
    const key = `${LOGIN_PROTECTION_CONFIG.keyPrefix}${ip}`;
    const current = await getLoginAttempts(ip);
    const now = Date.now();
    
    const newCount = current.count + 1;
    let lockedUntil: number | undefined;
    
    // 如果达到最大尝试次数，设置锁定时间
    if (newCount >= LOGIN_PROTECTION_CONFIG.maxAttempts) {
      lockedUntil = now + (LOGIN_PROTECTION_CONFIG.lockoutDuration * 1000);
    }
    
    const record = {
      count: newCount,
      lockedUntil,
      lastAttempt: now
    };
    
    // 设置过期时间为尝试窗口
    await redis.setEx(key, LOGIN_PROTECTION_CONFIG.attemptWindow, JSON.stringify(record));
    
    return record;
  } catch (error) {
    console.error('记录登录失败失败:', error);
    return { count: 0 };
  }
}

// 清除登录尝试记录（登录成功时调用）
async function clearLoginAttempts(ip: string): Promise<void> {
  try {
    const key = `${LOGIN_PROTECTION_CONFIG.keyPrefix}${ip}`;
    await redis.del(key);
  } catch (error) {
    console.error('清除登录尝试记录失败:', error);
  }
}

// 检查IP是否被锁定
async function isIPLocked(ip: string): Promise<{ locked: boolean; remainingTime?: number; attempts?: number }> {
  const record = await getLoginAttempts(ip);
  const now = Date.now();
  
  if (record.lockedUntil && record.lockedUntil > now) {
    return {
      locked: true,
      remainingTime: Math.ceil((record.lockedUntil - now) / 1000),
      attempts: record.count
    };
  }
  
  return {
    locked: false,
    attempts: record.count
  };
}

// 登录保护中间件
export const loginProtectionMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  const ip = getClientIP(req);
  
  try {
    const lockStatus = await isIPLocked(ip);
    
    if (lockStatus.locked) {
      return res.status(429).json({
        success: false,
        message: '登录尝试次数过多，账户已被临时锁定',
        error: 'TOO_MANY_ATTEMPTS',
        data: {
          remainingTime: lockStatus.remainingTime,
          maxAttempts: LOGIN_PROTECTION_CONFIG.maxAttempts
        }
      });
    }
    
    // 将IP和当前尝试次数添加到请求对象
    (req as any).clientIP = ip;
    (req as any).loginAttempts = lockStatus.attempts || 0;
    
    next();
  } catch (error) {
    console.error('登录保护中间件错误:', error);
    // 发生错误时不阻止登录，但记录错误
    next();
  }
};

// 处理登录失败的函数
export const handleLoginFailure = async (req: Request): Promise<{ count: number; lockedUntil?: number }> => {
  const ip = (req as any).clientIP || getClientIP(req);
  return await recordLoginFailure(ip);
};

// 处理登录成功的函数
export const handleLoginSuccess = async (req: Request): Promise<void> => {
  const ip = (req as any).clientIP || getClientIP(req);
  await clearLoginAttempts(ip);
};

// 获取剩余尝试次数
export const getRemainingAttempts = (currentAttempts: number): number => {
  return Math.max(0, LOGIN_PROTECTION_CONFIG.maxAttempts - currentAttempts);
};

export { LOGIN_PROTECTION_CONFIG };
