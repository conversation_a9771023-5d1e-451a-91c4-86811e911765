// 认证相关API路由
import express, { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { z } from 'zod';
import prisma from '../lib/prisma';
import { VerificationService } from '../services/verificationService';
import {
  loginProtectionMiddleware,
  handleLoginFailure,
  handleLoginSuccess,
  getRemainingAttempts
} from '../middleware/loginProtection';

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

// 请求验证模式
const registerSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(6, '密码至少6位'),
  name: z.string().optional()
});

const loginSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(1, '密码不能为空')
});

const loginWithCodeSchema = z.object({
  identifier: z.string().min(1, '邮箱或手机号不能为空'),
  code: z.string().length(6, '验证码必须是6位'),
  type: z.enum(['EMAIL', 'SMS']).default('EMAIL')
});

const sendCodeSchema = z.object({
  identifier: z.string().min(1, '邮箱或手机号不能为空'),
  type: z.enum(['EMAIL', 'SMS']).default('EMAIL')
});

/**
 * 用户注册
 */
router.post('/register', async (req: Request, res: Response) => {
  try {
    console.log('🔐 Auth - Register request received');
    const { email, password, name } = registerSchema.parse(req.body);

    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '该邮箱已被注册'
      });
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12);

    // 创建用户
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name: name || email.split('@')[0],
        balance: {
          create: {
            mockInterviewCredits: 2, // 新用户默认2次模拟面试
            formalInterviewCredits: 0, // 新用户默认0次正式面试
            mianshijunBalance: 0
          }
        }
      },
      include: { balance: true }
    });

    // 生成JWT token
    const payload = {
      userId: user.id,
      email: user.email,
      name: user.name
    };

    const token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

    // 移除密码字段
    const { password: _, ...userWithoutPassword } = user;

    console.log('✅ Auth - User registered successfully:', user.id);

    res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        token,
        user: userWithoutPassword,
        expiresIn: 24 * 60 * 60 // 24小时，单位秒
      }
    });
  } catch (error: any) {
    console.error('❌ Auth - Registration failed:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数错误',
        errors: error.errors
      });
    }

    res.status(500).json({
      success: false,
      message: '注册失败，请稍后再试'
    });
  }
});

/**
 * 用户登录（包含管理员登录保护）
 */
router.post('/login', loginProtectionMiddleware, async (req: Request, res: Response) => {
  try {
    console.log('🔐 Auth - Login request received');
    const { email, password } = loginSchema.parse(req.body);

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email },
      include: { balance: true }
    });

    if (!user) {
      // 记录登录失败
      const failureRecord = await handleLoginFailure(req);
      const remainingAttempts = getRemainingAttempts(failureRecord.count);

      return res.status(401).json({
        success: false,
        message: '邮箱或密码不正确',
        data: {
          remainingAttempts,
          maxAttempts: 5
        }
      });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      // 记录登录失败
      const failureRecord = await handleLoginFailure(req);
      const remainingAttempts = getRemainingAttempts(failureRecord.count);

      return res.status(401).json({
        success: false,
        message: '邮箱或密码不正确',
        data: {
          remainingAttempts,
          maxAttempts: 5,
          lockedUntil: failureRecord.lockedUntil
        }
      });
    }

    // 登录成功，清除失败记录
    await handleLoginSuccess(req);

    // 生成JWT token
    const payload = {
      userId: user.id,
      email: user.email,
      name: user.name
    };

    const token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

    // 移除密码字段
    const { password: _, ...userWithoutPassword } = user;

    console.log('✅ Auth - User logged in successfully:', user.id);

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: userWithoutPassword,
        expiresIn: 24 * 60 * 60 // 24小时，单位秒
      }
    });
  } catch (error: any) {
    console.error('❌ Auth - Login failed:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数错误',
        errors: error.errors
      });
    }

    res.status(500).json({
      success: false,
      message: '登录失败，请稍后再试'
    });
  }
});

/**
 * 发送验证码
 */
router.post('/send-verification-code', async (req: Request, res: Response) => {
  try {
    console.log('🔐 Auth - Send verification code request received');
    const { identifier, type } = sendCodeSchema.parse(req.body);

    const verificationService = new VerificationService();
    const result = await verificationService.sendCode(identifier, type, 'LOGIN');

    res.status(result.success ? 200 : 400).json(result);
  } catch (error: any) {
    console.error('❌ Auth - Send verification code failed:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数错误',
        errors: error.errors
      });
    }

    res.status(500).json({
      success: false,
      message: '发送验证码失败，请稍后再试'
    });
  }
});

/**
 * 验证码登录
 */
router.post('/login-with-code', async (req: Request, res: Response) => {
  try {
    console.log('🔐 Auth - Login with code request received');
    const { identifier, code, type } = loginWithCodeSchema.parse(req.body);

    // 验证验证码
    const verificationService = new VerificationService();
    const verifyResult = await verificationService.verifyCode(identifier, code, type, 'LOGIN');

    if (!verifyResult.success) {
      return res.status(400).json({
        success: false,
        message: verifyResult.message,
        error: {
          code: verifyResult.isLocked ? 'ACCOUNT_LOCKED' : 'INVALID_CODE'
        }
      });
    }

    // 查找或创建用户
    let user = await prisma.user.findFirst({
      where: type === 'EMAIL'
        ? { email: identifier }
        : { phoneNumber: identifier },
      include: { balance: true }
    });

    // 如果用户不存在，自动创建新用户
    if (!user) {
      console.log('🔐 Auth - Creating new user for:', identifier);
      
      const userData = type === 'EMAIL' 
        ? { email: identifier, name: identifier.split('@')[0] }
        : { phoneNumber: identifier, name: `用户${identifier.slice(-4)}` };

      user = await prisma.user.create({
        data: {
          ...userData,
          password: '', // 验证码登录不需要密码
          balance: {
            create: {
              mockInterviewCredits: 2, // 新用户默认2次模拟面试
              formalInterviewCredits: 0, // 新用户默认0次正式面试
              mianshijunBalance: 0
            }
          }
        },
        include: { balance: true }
      });
      
      console.log('✅ Auth - New user created:', user.id);
    }

    // 生成JWT token
    const payload = {
      userId: user.id,
      email: user.email,
      name: user.name
    };

    const token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

    // 移除密码字段
    const { password: _, ...userWithoutPassword } = user;

    console.log('✅ Auth - User logged in with code successfully:', user.id);

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: userWithoutPassword,
        expiresIn: 24 * 60 * 60 // 24小时，单位秒
      }
    });
  } catch (error: any) {
    console.error('❌ Auth - Login with code failed:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数错误',
        errors: error.errors
      });
    }

    res.status(500).json({
      success: false,
      message: '登录失败，请稍后再试'
    });
  }
});

export default router;
