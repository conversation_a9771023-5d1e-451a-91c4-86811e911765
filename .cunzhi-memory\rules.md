# 开发规范和规则

- 文档管理规范：所有项目文档必须写在 E:\Data\Own\Entrepreneurship\local-mianshijun\docs 目录下，不得在其他位置创建文档文件。临时调试工具、测试脚本等开发辅助文件在完成任务后必须清理删除，保持项目结构整洁。
- UI设计不可变更原则：严格禁止在主要用户界面中添加调试面板、性能监控组件或其他开发工具。如需调试功能，必须创建独立页面或在完成后完全移除，确保生产环境UI的纯净性。
- AI模拟面试重构需求确认：1.开始按钮位置在会话页面中央 2.流程为配置→开始面试→会话页面→开始模拟面试按钮→LLM对话 3.移除WebSocket音频处理，使用现有聊天UI组件实现蓝绿气泡对话
- 用户明确要求：❌不要生成总结性Markdown文档，❌不要生成测试脚本，✔️帮助编译和运行代码
- 用户明确要求：❌不要生成总结性Markdown文档，❌不要生成测试脚本，✔️帮助编译和运行代码。代码迁移任务：修改之前必须通过寸止MCP与用户确认，如有冲突完全依照技术团队代码为准
- 用户强调：❌不要生成总结性Markdown文档，❌不要生成测试脚本，✔️帮助编译和运行代码。技术团队的代码调试通过，我们需要找出为什么我们的编译不通过
- 用户强调：❌不要生成总结性Markdown文档，❌不要生成测试脚本，✔️帮助编译，✔️帮助运行代码
- AI模拟面试修复方案：统一两种面试的启动流程，让模拟面试也先调用API创建数据库记录然后再进行WebSocket连接，算法逻辑完全参考AI正式面试，前端UI暂时不改动
- 用户强调：如果方案涉及UI改动必须逐一商量确认，不要生成总结性Markdown文档，不要生成测试脚本，需要帮助编译和运行
