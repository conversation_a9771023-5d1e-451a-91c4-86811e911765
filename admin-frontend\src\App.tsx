import React, { useState, useEffect } from 'react';
import {
  HomeIcon,
  UsersIcon,
  ShoppingCartIcon,
  UserPlusIcon,
  DocumentTextIcon,
  CogIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  ServerIcon,
  ExclamationTriangleIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  TicketIcon
} from '@heroicons/react/24/outline';

import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { ToastProvider, useToastContext } from './contexts/ToastContext';

// API 配置 - 根据环境自动选择
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://mianshijun.xyz/api'  // 生产环境
  : 'http://localhost:3000/api';  // 开发环境

// 类型定义
interface AdminUser {
  id: string;
  name?: string;
  email: string;
  role: string;
}

interface RedemptionCode {
  id: string;
  code: string;
  status: 'unused' | 'used' | 'expired' | 'disabled';
  benefitType: 'POINTS' | 'MOCK_INTERVIEW' | 'FORMAL_INTERVIEW';
  benefitAmount: number;
  usageCount: number;
  usageLimit: number;
  expiresAt: string | null;
  createdAt: string;
  updatedAt?: string;
  isActive: boolean;
  description?: string;
}

// Token验证函数
const verifyToken = async () => {
  const token = localStorage.getItem('adminToken');
  if (!token) return false;

  try {
    // 尝试调用一个需要认证的API来验证token
    const response = await fetch(`${API_BASE_URL}/admin/notifications`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    return response.ok;
  } catch (error) {
    console.error('Token验证失败:', error);
    return false;
  }
};

// API 调用函数（带自动token刷新）
const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('adminToken');

  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    },
    ...options,
  };

  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, defaultOptions);

    // 如果token过期，清除登录状态
    if (response.status === 401) {
      console.log('Token已过期，清除登录状态');
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminUser');
      window.location.reload();
      throw new Error('登录已过期，请重新登录');
    }

    const data = await response.json();
    if (!response.ok) {
      throw new Error(data.message || '请求失败');
    }

    return data;
  } catch (error) {
    console.error('API调用失败:', error);
    throw error;
  }
};

// 模拟数据
const dashboardData = {
  totalUsers: 12580,
  newUsers: 156,
  paidUsers: 2340,
  newPaidUsers: 23,
  totalRevenue: 89650,
  dailyRevenue: 1250,
  deepseekBalance: 8500,
  baiduBalance: 6200
};

const chartData = [
  { name: '1月', users: 400, revenue: 2400 },
  { name: '2月', users: 300, revenue: 1398 },
  { name: '3月', users: 200, revenue: 9800 },
  { name: '4月', users: 278, revenue: 3908 },
  { name: '5月', users: 189, revenue: 4800 },
  { name: '6月', users: 239, revenue: 3800 },
];

const orders = [
  { id: 'ORD001', user: '<EMAIL>', product: 'VIP会员', amount: 99, status: '已支付', payMethod: '微信支付', createTime: '2024-01-15 10:30' },
  { id: 'ORD002', user: '<EMAIL>', product: '面试次数包', amount: 29, status: '已支付', payMethod: '支付宝', createTime: '2024-01-16 14:20' },
  { id: 'ORD003', user: '<EMAIL>', product: 'VIP会员', amount: 99, status: '待支付', payMethod: '微信支付', createTime: '2024-01-17 09:15' },
];

// 内部App组件，使用Toast Context
function AppContent() {
  const [activeMenu, setActiveMenu] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);

  // 使用新的Toast系统
  const { showSuccess, showError } = useToastContext();

  // 登录状态
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [loginForm, setLoginForm] = useState({
    email: '',
    password: ''
  });

  // 兑换码相关状态
  const [redemptionCodes, setRedemptionCodes] = useState<RedemptionCode[]>([]);
  const [loading, setLoading] = useState(false);

  // 用户列表状态管理
  const [users, setUsers] = useState<any[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const [usersError, setUsersError] = useState<string | null>(null);
  const [usersPagination, setUsersPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  });

  // 表单状态
  const [formData, setFormData] = useState({
    benefitType: '',
    benefitAmount: '',
    expiresInDays: '',
    usageLimit: '1',
    codePrefix: '',
    generateCount: '1',
    description: ''
  });

  // 兑换码列表功能状态
  const [selectedCodes, setSelectedCodes] = useState<string[]>([]);
  const [searchFilters, setSearchFilters] = useState({
    searchText: '',
    status: '',
    benefitType: ''
  });
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmAction, setConfirmAction] = useState<{
    type: 'delete' | 'batchDelete' | 'disable' | 'enable';
    id?: string;
    title: string;
    message: string;
    onConfirm: () => void;
  } | null>(null);
  const [editingCode, setEditingCode] = useState<RedemptionCode | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [viewingCode, setViewingCode] = useState<RedemptionCode | null>(null);

  // 分页状态
  const [codeListPagination, setCodeListPagination] = useState({
    currentPage: 1,
    itemsPerPage: 20,
    totalItems: 0,
    totalPages: 0
  });
  const [usageRecordsPagination, setUsageRecordsPagination] = useState({
    currentPage: 1,
    itemsPerPage: 20,
    totalItems: 0,
    totalPages: 0
  });

  // 通知管理相关状态
  const [showCreateNotificationDialog, setShowCreateNotificationDialog] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [notificationForm, setNotificationForm] = useState({
    title: '',
    content: '',
    type: 'ANNOUNCEMENT',
    priority: 'NORMAL',
    expiresAt: '',
    targetType: 'ALL_USERS',
    targetValue: ''
  });



  // 创建通知函数
  const handleCreateNotification = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // 验证必填字段
      if (!notificationForm.title.trim()) {
        showError('请输入通知标题');
        return;
      }
      if (!notificationForm.content.trim()) {
        showError('请输入通知内容');
        return;
      }

      const response = await apiCall('/admin/notifications', {
        method: 'POST',
        body: JSON.stringify({
          title: notificationForm.title.trim(),
          content: notificationForm.content.trim(),
          type: notificationForm.type,
          priority: notificationForm.priority,
          targetType: notificationForm.targetType,
          targetValue: notificationForm.targetValue || '',
          expiresAt: notificationForm.expiresAt || undefined
        })
      });

      if (response.success) {
        showSuccess('通知创建并发布成功！');
        setShowCreateNotificationDialog(false);
        // 重置表单
        setNotificationForm({
          title: '',
          content: '',
          type: 'ANNOUNCEMENT',
          priority: 'NORMAL',
          targetType: 'ALL_USERS',
          targetValue: '',
          expiresAt: ''
        });
        // 刷新通知列表
        fetchNotifications();
      } else {
        throw new Error(response.message || '创建通知失败');
      }
    } catch (error: any) {
      console.error('创建通知失败:', error);
      showError(error.message || '创建通知失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 获取通知列表
  const fetchNotifications = async () => {
    try {
      const response = await apiCall('/admin/notifications');
      if (response.success) {
        setNotifications(response.data.notifications || []);
      } else {
        console.error('获取通知列表失败:', response.message);
      }
    } catch (error: any) {
      console.error('获取通知列表失败:', error);
    }
  };

  // 检查登录状态
  useEffect(() => {
    const checkLoginStatus = async () => {
      const token = localStorage.getItem('adminToken');
      const user = localStorage.getItem('adminUser');

      if (token && user && user !== 'undefined' && user !== 'null') {
        try {
          // 使用后端API验证token
          const isValid = await verifyToken();

          if (isValid) {
            setIsLoggedIn(true);
            setAdminUser(JSON.parse(user));
          } else {
            // Token无效，清除本地存储
            localStorage.removeItem('adminToken');
            localStorage.removeItem('adminUser');
            showError('登录已过期，请重新登录');
          }
        } catch (error) {
          console.error('验证token失败:', error);
          // 清除无效的localStorage数据
          localStorage.removeItem('adminToken');
          localStorage.removeItem('adminUser');
          showError('登录验证失败，请重新登录');
        }
      }

      setIsCheckingAuth(false);
    };

    if (isCheckingAuth) {
      checkLoginStatus();
    }
  }, [isCheckingAuth]);

  // 当切换到通知管理页面时加载数据
  useEffect(() => {
    if (activeMenu === 'notification-management' && isLoggedIn) {
      fetchNotifications();
    }
  }, [activeMenu, isLoggedIn]);

  // 登录函数
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginForm),
      });

      const data = await response.json();

      if (!response.ok) {
        // 处理登录失败，显示剩余尝试次数
        let errorMessage = data.message || '登录失败';

        if (data.data?.remainingAttempts !== undefined) {
          if (data.data.remainingAttempts > 0) {
            errorMessage += `，剩余尝试次数：${data.data.remainingAttempts}次`;
          } else if (data.data.lockedUntil) {
            const lockTime = Math.ceil((data.data.lockedUntil - Date.now()) / 1000 / 60);
            errorMessage = `登录尝试次数过多，账户已被锁定${lockTime}分钟`;
          }
        }

        if (response.status === 429) {
          // 账户被锁定，显示倒计时Toast
          const remainingTime = data.data?.remainingTime || 1800; // 默认30分钟
          showError(errorMessage, remainingTime * 1000); // 转换为毫秒
        } else {
          showError(errorMessage);
        }

        throw new Error(errorMessage);
      }

      // 检查响应数据结构，支持两种格式
      let token, user;
      if (data.data) {
        // 标准响应格式：{ success: true, data: { token, user } }
        token = data.data.token;
        user = data.data.user;
      } else {
        // 简单响应格式：{ token, user }
        token = data.token;
        user = data.user;
      }

      // 验证必要数据
      if (!token) {
        throw new Error('登录失败：未收到有效的认证令牌');
      }
      if (!user) {
        throw new Error('登录失败：未收到有效的用户信息');
      }

      // 检查管理员权限
      if (user.role !== 'ADMIN') {
        throw new Error('访问被拒绝：您没有管理员权限');
      }

      // 保存登录信息
      localStorage.setItem('adminToken', token);
      localStorage.setItem('adminUser', JSON.stringify(user));

      setIsLoggedIn(true);
      setAdminUser(user);
      showSuccess('登录成功！');

    } catch (error: any) {
      // 根据错误类型提供友好的错误提示
      let errorMessage = '登录失败，请重试';

      if (error.message) {
        if (error.message.includes('网络')) {
          errorMessage = '网络连接失败，请检查网络设置后重试';
        } else if (error.message.includes('权限')) {
          errorMessage = '您没有管理员权限，请联系系统管理员';
        } else if (error.message.includes('密码') || error.message.includes('邮箱')) {
          errorMessage = '邮箱或密码错误，请检查后重试';
        } else if (error.message.includes('服务器')) {
          errorMessage = '服务器暂时无法访问，请稍后重试';
        } else {
          errorMessage = error.message;
        }
      }

      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 登出函数
  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');
    setIsLoggedIn(false);
    setAdminUser(null);
    setActiveMenu('dashboard');
  };

  // 获取用户列表
  const fetchUsers = async (page = 1, pageSize = 10, search = '', status = 'all', level = 'all') => {
    if (!isLoggedIn) return;

    setUsersLoading(true);
    setUsersError(null);

    try {
      const token = localStorage.getItem('adminToken');
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        search,
        status,
        level
      });

      const response = await fetch(`${API_BASE_URL}/admin/users?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('获取用户列表失败');
      }

      const data = await response.json();
      if (data.success) {
        setUsers(data.data.users);
        setUsersPagination({
          page: data.data.page,
          pageSize: data.data.pageSize,
          total: data.data.total,
          totalPages: data.data.totalPages
        });
      } else {
        throw new Error(data.message || '获取用户列表失败');
      }
    } catch (error: any) {
      console.error('获取用户列表失败:', error);
      setUsersError(error.message || '获取用户列表失败');
    } finally {
      setUsersLoading(false);
    }
  };

  // 当切换到用户列表页面时获取数据
  useEffect(() => {
    if (activeMenu === 'user-list' && isLoggedIn) {
      fetchUsers();
    }
  }, [activeMenu, isLoggedIn]);

  // 生成兑换码API调用
  const handleGenerateCodes = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // 验证表单数据
      if (!formData.benefitType || !formData.benefitAmount) {
        throw new Error('请填写奖励类型和数量');
      }

      const requestData = {
        benefitType: formData.benefitType,
        benefitAmount: parseInt(formData.benefitAmount),
        expiresInDays: formData.expiresInDays ? parseInt(formData.expiresInDays) : undefined,
        usageLimit: parseInt(formData.usageLimit),
        codePrefix: formData.codePrefix || undefined,
        generateCount: parseInt(formData.generateCount),
        description: formData.description || undefined
      };

      const response = await apiCall('/admin/redemption-codes', {
        method: 'POST',
        body: JSON.stringify(requestData)
      });

      // 处理响应数据结构
      const codes = response.data?.codes || response.codes || [];
      showSuccess(`成功生成 ${codes.length} 个兑换码！`);

      // 重置表单
      setFormData({
        benefitType: '',
        benefitAmount: '',
        expiresInDays: '',
        usageLimit: '1',
        codePrefix: '',
        generateCount: '1',
        description: ''
      });

      // 显示生成的兑换码
      console.log('生成的兑换码:', codes);

      // 跳转到兑换码列表页面
      setActiveMenu('code-list');

      // 刷新兑换码列表数据
      await fetchRedemptionCodes();

    } catch (error: any) {
      showError(error.message || '生成兑换码失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 计算兑换码状态
  const calculateCodeStatus = (code: any) => {
    if (!code.isActive) return 'disabled';
    if (code.usageCount >= code.usageLimit) return 'used';
    if (code.expiresAt && new Date(code.expiresAt) < new Date()) return 'expired';
    return 'unused';
  };

  // 获取兑换码列表
  const fetchRedemptionCodes = async (page = 1, filters = {}) => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: codeListPagination.itemsPerPage.toString(),
        ...filters
      });

      const response = await apiCall(`/admin/redemption-codes?${queryParams}`);

      // 处理统一的API响应格式
      const data = response.data || response;
      const codes = data.codes || data;
      const pagination = response.pagination || data.pagination;

      // 为每个兑换码添加计算出的状态
      const codesWithStatus = codes.map((code: any) => ({
        ...code,
        status: calculateCodeStatus(code)
      }));

      setRedemptionCodes(codesWithStatus);

      // 更新分页信息
      setCodeListPagination(prev => ({
        ...prev,
        currentPage: page,
        totalItems: pagination?.total || codesWithStatus.length,
        totalPages: Math.ceil((pagination?.total || codesWithStatus.length) / prev.itemsPerPage)
      }));

      return response;
    } catch (error: any) {
      showError(error.message || '获取兑换码列表失败');
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 获取使用记录
  const fetchUsageRecords = async (page = 1, filters = {}) => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: usageRecordsPagination.itemsPerPage.toString(),
        ...filters
      });

      const response = await apiCall(`/admin/redemption-codes/usage-records?${queryParams}`);

      // 更新分页信息
      setUsageRecordsPagination(prev => ({
        ...prev,
        currentPage: page,
        totalItems: response.total || response.data.length,
        totalPages: Math.ceil((response.total || response.data.length) / prev.itemsPerPage)
      }));

      return response;
    } catch (error: any) {
      showError(error.message || '获取使用记录失败');
      return null;
    } finally {
      setLoading(false);
    }
  };



  // 切换菜单展开状态
  const toggleMenuExpansion = (menuId: string) => {
    setExpandedMenus(prev =>
      prev.includes(menuId)
        ? prev.filter(id => id !== menuId)
        : [...prev, menuId]
    );
  };

  // 兑换码列表功能函数

  // 复制兑换码到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      showSuccess('兑换码已复制到剪贴板');
    } catch (error) {
      showError('复制失败，请手动复制');
    }
  };

  // 搜索兑换码
  const handleSearch = async () => {
    setLoading(true);
    try {
      const filters: any = {};
      if (searchFilters.searchText) filters.search = searchFilters.searchText;
      if (searchFilters.status) filters.status = searchFilters.status;
      if (searchFilters.benefitType) filters.benefitType = searchFilters.benefitType;

      await fetchRedemptionCodes(1, filters);
    } catch (error: any) {
      showError(error.message || '搜索失败');
    } finally {
      setLoading(false);
    }
  };

  // 兑换码列表分页处理函数
  const handleCodeListPageChange = async (newPage: number) => {
    if (newPage < 1 || newPage > codeListPagination.totalPages) return;

    const filters: any = {};
    if (searchFilters.searchText) filters.search = searchFilters.searchText;
    if (searchFilters.status) filters.status = searchFilters.status;
    if (searchFilters.benefitType) filters.benefitType = searchFilters.benefitType;

    await fetchRedemptionCodes(newPage, filters);
  };

  // 使用记录分页处理函数
  const handleUsageRecordsPageChange = async (newPage: number) => {
    if (newPage < 1 || newPage > usageRecordsPagination.totalPages) return;
    await fetchUsageRecords(newPage);
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCodes(redemptionCodes.map((code: any) => code.id));
    } else {
      setSelectedCodes([]);
    }
  };

  // 单选兑换码
  const handleSelectCode = (codeId: string, checked: boolean) => {
    if (checked) {
      setSelectedCodes(prev => [...prev, codeId]);
    } else {
      setSelectedCodes(prev => prev.filter(id => id !== codeId));
    }
  };

  // 查看兑换码详情
  const handleViewCode = (code: any) => {
    setViewingCode(code);
    setShowViewDialog(true);
  };

  // 编辑兑换码
  const handleEditCode = (code: any) => {
    setEditingCode(code);
    setShowEditDialog(true);
  };

  // 保存编辑
  const handleSaveEdit = async () => {
    if (!editingCode) return;

    setLoading(true);
    try {
      await apiCall(`/admin/redemption-codes/${editingCode.id}`, {
        method: 'PATCH',
        body: JSON.stringify({
          description: editingCode.description,
          expiresAt: editingCode.expiresAt,
          usageLimit: editingCode.usageLimit
        })
      });

      showSuccess('兑换码更新成功');
      setShowEditDialog(false);
      setEditingCode(null);
      await fetchRedemptionCodes();
    } catch (error: any) {
      showError(error.message || '更新失败');
    } finally {
      setLoading(false);
    }
  };

  // 禁用/启用兑换码
  const handleToggleCodeStatus = (code: any) => {
    const action = !code.isActive ? 'enable' : 'disable';
    const actionText = action === 'enable' ? '启用' : '禁用';

    setConfirmAction({
      type: action,
      id: code.id,
      title: `${actionText}兑换码`,
      message: `确定要${actionText}兑换码 "${code.code}" 吗？`,
      onConfirm: () => executeToggleStatus(code.id, action)
    });
    setShowConfirmDialog(true);
  };

  const executeToggleStatus = async (codeId: string, action: 'enable' | 'disable') => {
    setLoading(true);
    try {
      await apiCall(`/admin/redemption-codes/${codeId}/${action}`, {
        method: 'PATCH'
      });

      showSuccess(`兑换码${action === 'enable' ? '启用' : '禁用'}成功`);
      setShowConfirmDialog(false);
      setConfirmAction(null);
      await fetchRedemptionCodes();
    } catch (error: any) {
      showError(error.message || `${action === 'enable' ? '启用' : '禁用'}失败`);
    } finally {
      setLoading(false);
    }
  };

  // 删除单个兑换码
  const handleDeleteCode = (code: any) => {
    setConfirmAction({
      type: 'delete',
      id: code.id,
      title: '删除兑换码',
      message: `确定要删除兑换码 "${code.code}" 吗？此操作不可撤销。`,
      onConfirm: () => executeDeleteCode(code.id)
    });
    setShowConfirmDialog(true);
  };

  const executeDeleteCode = async (codeId: string) => {
    setLoading(true);
    // 显示删除进度提示
    showSuccess('正在删除中......');
    try {
      await apiCall(`/admin/redemption-codes/${codeId}`, {
        method: 'DELETE'
      });

      showSuccess('兑换码删除成功');
      await fetchRedemptionCodes();
    } catch (error: any) {
      showError(error.message || '删除失败');
    } finally {
      setLoading(false);
    }
  };

  // 批量删除兑换码
  const handleBatchDelete = () => {
    if (selectedCodes.length === 0) {
      showError('请先选择要删除的兑换码');
      return;
    }

    setConfirmAction({
      type: 'batchDelete',
      title: '批量删除兑换码',
      message: `确定要删除选中的 ${selectedCodes.length} 个兑换码吗？此操作不可撤销。`,
      onConfirm: executeBatchDelete
    });
    setShowConfirmDialog(true);
  };

  const executeBatchDelete = async () => {
    setLoading(true);
    // 显示删除进度提示
    showSuccess('正在删除中......');
    try {
      await apiCall('/admin/redemption-codes/batch-delete', {
        method: 'DELETE',
        body: JSON.stringify({ ids: selectedCodes })
      });

      showSuccess(`成功删除 ${selectedCodes.length} 个兑换码`);
      setSelectedCodes([]);
      await fetchRedemptionCodes();
    } catch (error: any) {
      showError(error.message || '批量删除失败');
    } finally {
      setLoading(false);
    }
  };

  // 导出兑换码列表
  const handleExportList = async () => {
    setLoading(true);
    try {
      const response = await apiCall('/admin/redemption-codes/export', {
        method: 'GET'
      });

      // 创建下载链接
      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `兑换码列表_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showSuccess('兑换码列表导出成功');
    } catch (error: any) {
      showError(error.message || '导出失败');
    } finally {
      setLoading(false);
    }
  };



  // 当切换到兑换码列表时自动加载数据
  useEffect(() => {
    if (activeMenu === 'code-list' && isLoggedIn) {
      fetchRedemptionCodes();
    }
  }, [activeMenu, isLoggedIn]);

  // 当切换到使用记录时自动加载数据
  useEffect(() => {
    if (activeMenu === 'usage-records' && isLoggedIn) {
      fetchUsageRecords();
    }
  }, [activeMenu, isLoggedIn]);

  // 登录页面组件
  const renderLoginPage = () => (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <TicketIcon className="h-12 w-12 text-blue-600" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
          AI面试君管理系统
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          请使用管理员账户登录
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleLogin}>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                邮箱地址
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={loginForm.email}
                  onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入管理员邮箱"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                密码
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={loginForm.password}
                  onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入密码"
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? '登录中...' : '登录'}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">测试账户</span>
              </div>
            </div>

            <div className="mt-4 text-sm text-gray-600 bg-gray-50 p-4 rounded-md">
              <p className="font-medium">管理员测试账户：</p>
              <p>邮箱：<EMAIL></p>
              <p>密码：admin123</p>
              <p className="text-xs text-gray-500 mt-2">
                注意：请确保该账户在数据库中存在且角色为ADMIN
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const menuItems = [
    { id: 'dashboard', name: '工作台', icon: HomeIcon, children: [] },
    {
      id: 'users',
      name: '用户管理',
      icon: UsersIcon,
      children: [
        { id: 'user-list', name: '用户列表' },
        { id: 'user-details', name: '用户详情' }
      ]
    },
    {
      id: 'orders',
      name: '订单管理',
      icon: ShoppingCartIcon,
      children: [
        { id: 'order-list', name: '订单列表' },
        { id: 'order-details', name: '订单详情' }
      ]
    },
    {
      id: 'invitations',
      name: '邀请管理',
      icon: UserPlusIcon,
      children: [
        { id: 'invitation-records', name: '邀请记录' },
        { id: 'reward-records', name: '奖励记录' },
        { id: 'invitation-stats', name: '邀请统计' }
      ]
    },
    {
      id: 'redemption',
      name: '兑换管理',
      icon: TicketIcon,
      children: [
        { id: 'generate-codes', name: '生成兑换码' },
        { id: 'code-list', name: '兑换码列表' },
        { id: 'usage-records', name: '使用记录' },
        { id: 'redemption-stats', name: '统计报表' }
      ]
    },
    {
      id: 'logs',
      name: '日志管理',
      icon: DocumentTextIcon,
      children: [
        { id: 'operation-logs', name: '操作日志' },
        { id: 'system-logs', name: '系统日志' },
        { id: 'security-logs', name: '安全日志' }
      ]
    },
    {
      id: 'settings',
      name: '系统设置',
      icon: CogIcon,
      children: [
        { id: 'api-config', name: 'API配置' },
        { id: 'payment-config', name: '支付配置' },
        { id: 'system-params', name: '系统参数' },
        { id: 'notification-management', name: '通知管理' }
      ]
    }
  ];

  const renderDashboard = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">工作台</h1>
        <div className="text-sm text-gray-500">
          最后更新: {new Date().toLocaleString()}
        </div>
      </div>

      {/* 数据概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UsersIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">用户总数</p>
              <p className="text-2xl font-bold text-gray-900">{dashboardData.totalUsers.toLocaleString()}</p>
              <p className="text-sm text-green-600">+{dashboardData.newUsers} 今日新增</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">付费用户</p>
              <p className="text-2xl font-bold text-gray-900">{dashboardData.paidUsers.toLocaleString()}</p>
              <p className="text-sm text-green-600">+{dashboardData.newPaidUsers} 今日新增</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">总收入</p>
              <p className="text-2xl font-bold text-gray-900">¥{dashboardData.totalRevenue.toLocaleString()}</p>
              <p className="text-sm text-green-600">+¥{dashboardData.dailyRevenue} 今日</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ServerIcon className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">系统状态</p>
              <p className="text-2xl font-bold text-green-600">正常</p>
              <p className="text-sm text-gray-500">所有服务运行正常</p>
            </div>
          </div>
        </div>
      </div>

      {/* API余额监控 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-3">API余额监控</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">DeepSeek API</span>
              <div className="flex items-center">
                <span className="text-lg font-bold text-gray-900">¥{dashboardData.deepseekBalance}</span>
                <div className="ml-2 w-2 h-2 bg-green-400 rounded-full"></div>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">百度ASR API</span>
              <div className="flex items-center">
                <span className="text-lg font-bold text-gray-900">¥{dashboardData.baiduBalance}</span>
                <div className="ml-2 w-2 h-2 bg-yellow-400 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-3">系统状态</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">服务器状态</span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                正常
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">数据库状态</span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                正常
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">API调用状态</span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                正常
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">用户增长趋势</h3>
          <ResponsiveContainer width="100%" height={200}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="users" stroke="#3B82F6" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">收入趋势</h3>
          <ResponsiveContainer width="100%" height={200}>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="revenue" fill="#10B981" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );

  const renderUserList = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">用户列表</h1>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
          添加用户
        </button>
      </div>

      {/* 错误提示 */}
      {usersError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{usersError}</p>
          <button
            onClick={() => fetchUsers()}
            className="mt-2 text-red-600 hover:text-red-800 underline"
          >
            重试
          </button>
        </div>
      )}

      <div className="bg-white rounded-lg shadow overflow-hidden">
        {/* 加载状态 */}
        {usersLoading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">加载中...</p>
          </div>
        ) : (
          <>
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">昵称</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">邮箱</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">会员等级</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">剩余次数</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">充值金额</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">注册时间</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.length === 0 ? (
                  <tr>
                    <td colSpan={9} className="px-6 py-8 text-center text-gray-500">
                      暂无用户数据
                    </td>
                  </tr>
                ) : (
                  users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {user.id.slice(-8)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.name}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.email}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          user.status === '正常' ? 'bg-green-100 text-green-800' :
                          user.status === '管理员' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {user.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.level}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.interviews}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥{user.balance}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.registerTime}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button className="text-blue-600 hover:text-blue-900 mr-3">编辑</button>
                        <button className="text-red-600 hover:text-red-900">禁用</button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>

            {/* 分页 */}
            {usersPagination.totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  显示 {((usersPagination.page - 1) * usersPagination.pageSize) + 1} 到{' '}
                  {Math.min(usersPagination.page * usersPagination.pageSize, usersPagination.total)} 条，
                  共 {usersPagination.total} 条记录
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => fetchUsers(usersPagination.page - 1)}
                    disabled={usersPagination.page <= 1}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    上一页
                  </button>
                  <span className="text-sm text-gray-600">
                    第 {usersPagination.page} 页，共 {usersPagination.totalPages} 页
                  </span>
                  <button
                    onClick={() => fetchUsers(usersPagination.page + 1)}
                    disabled={usersPagination.page >= usersPagination.totalPages}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    下一页
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );

  const renderOrderList = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">订单列表</h1>
        <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
          导出订单
        </button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单号</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商品</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">支付方式</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {orders.map((order) => (
              <tr key={order.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{order.id}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{order.user}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{order.product}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥{order.amount}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{order.payMethod}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    order.status === '已支付' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {order.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{order.createTime}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button className="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                  <button className="text-red-600 hover:text-red-900">退款</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderGenerateCodes = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">生成兑换码</h1>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <form className="space-y-6" onSubmit={handleGenerateCodes}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 奖励类型选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                奖励类型
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={formData.benefitType}
                onChange={(e) => setFormData({...formData, benefitType: e.target.value})}
                required
              >
                <option value="">请选择奖励类型</option>
                <option value="POINTS">面巾余额</option>
                <option value="MOCK_INTERVIEW">模拟面试次数</option>
                <option value="FORMAL_INTERVIEW">正式面试次数</option>
              </select>
            </div>

            {/* 奖励数量 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                奖励数量
              </label>
              <input
                type="number"
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入奖励数量"
                value={formData.benefitAmount}
                onChange={(e) => setFormData({...formData, benefitAmount: e.target.value})}
                required
              />
            </div>

            {/* 有效期设置 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                有效期（天）
              </label>
              <input
                type="number"
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入有效天数"
                value={formData.expiresInDays}
                onChange={(e) => setFormData({...formData, expiresInDays: e.target.value})}
              />
            </div>

            {/* 使用次数限制 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                使用次数限制
              </label>
              <input
                type="number"
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入使用次数限制"
                value={formData.usageLimit}
                onChange={(e) => setFormData({...formData, usageLimit: e.target.value})}
              />
            </div>

            {/* 兑换码前缀 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                兑换码前缀（可选）
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="如：VIP2024、WELCOME等"
                value={formData.codePrefix}
                onChange={(e) => setFormData({...formData, codePrefix: e.target.value})}
              />
            </div>

            {/* 生成数量 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                生成数量
              </label>
              <input
                type="number"
                min="1"
                max="100"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入生成数量（最多100个）"
                value={formData.generateCount}
                onChange={(e) => setFormData({...formData, generateCount: e.target.value})}
              />
            </div>
          </div>

          {/* 描述备注 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              描述备注（可选）
            </label>
            <textarea
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入兑换码的描述或备注信息"
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
            />
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => setFormData({
                benefitType: '',
                benefitAmount: '',
                expiresInDays: '',
                usageLimit: '1',
                codePrefix: '',
                generateCount: '1',
                description: ''
              })}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              disabled={loading}
            >
              重置
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? '生成中...' : '生成兑换码'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );

  const renderCodeList = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">兑换码列表</h1>
        <div className="flex space-x-3">
          <button
            onClick={handleExportList}
            disabled={loading}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
          >
            导出列表
          </button>
          <button
            onClick={handleBatchDelete}
            disabled={loading || selectedCodes.length === 0}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
          >
            批量删除
          </button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <input
              type="text"
              placeholder="搜索兑换码..."
              value={searchFilters.searchText}
              onChange={(e) => setSearchFilters(prev => ({ ...prev, searchText: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <select
              value={searchFilters.status}
              onChange={(e) => setSearchFilters(prev => ({ ...prev, status: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">全部状态</option>
              <option value="unused">未使用</option>
              <option value="used">已使用</option>
              <option value="expired">已过期</option>
              <option value="disabled">已禁用</option>
            </select>
          </div>
          <div>
            <select
              value={searchFilters.benefitType}
              onChange={(e) => setSearchFilters(prev => ({ ...prev, benefitType: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">全部类型</option>
              <option value="POINTS">面巾余额</option>
              <option value="MOCK_INTERVIEW">模拟面试次数</option>
              <option value="FORMAL_INTERVIEW">正式面试次数</option>
            </select>
          </div>
          <div>
            <button
              onClick={handleSearch}
              disabled={loading}
              className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              搜索
            </button>
          </div>
        </div>
      </div>

      {/* 兑换码列表表格 */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <input
                  type="checkbox"
                  className="rounded"
                  checked={redemptionCodes && redemptionCodes.length > 0 && selectedCodes.length === redemptionCodes.length}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">兑换码</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖励类型</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖励数量</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用情况</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">有效期</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {redemptionCodes && redemptionCodes.length > 0 ? (
              redemptionCodes.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      className="rounded"
                      checked={selectedCodes.includes(item.id)}
                      onChange={(e) => handleSelectCode(item.id, e.target.checked)}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-sm font-medium text-gray-900">{item.code}</span>
                      <button
                        onClick={() => copyToClipboard(item.code)}
                        className="ml-2 text-blue-600 hover:text-blue-800"
                        title="复制兑换码"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </button>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      item.status === 'unused' ? 'bg-green-100 text-green-800' :
                      item.status === 'used' ? 'bg-blue-100 text-blue-800' :
                      item.status === 'expired' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {item.status === 'unused' ? '未使用' :
                       item.status === 'used' ? '已使用' :
                       item.status === 'expired' ? '已过期' : '已禁用'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.benefitType === 'POINTS' ? '面巾余额' :
                     item.benefitType === 'MOCK_INTERVIEW' ? '模拟面试次数' : '正式面试次数'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.benefitAmount}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.usageCount}/{item.usageLimit}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.expiresAt}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.createdAt}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleViewCode(item)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        查看
                      </button>
                      <button
                        onClick={() => handleEditCode(item)}
                        className="text-green-600 hover:text-green-900"
                      >
                        编辑
                      </button>
                      <button
                        onClick={() => handleToggleCodeStatus(item)}
                        className="text-yellow-600 hover:text-yellow-900"
                      >
                        {!item.isActive ? '启用' : '禁用'}
                      </button>
                      <button
                        onClick={() => handleDeleteCode(item)}
                        className="text-red-600 hover:text-red-900"
                      >
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={9} className="px-6 py-12 text-center">
                  <div className="text-gray-500">
                    <TicketIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">暂无兑换码</h3>
                    <p className="text-sm text-gray-500">还没有生成任何兑换码，点击"生成兑换码"开始创建</p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* 分页 */}
      {codeListPagination.totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => handleCodeListPageChange(codeListPagination.currentPage - 1)}
              disabled={codeListPagination.currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <button
              onClick={() => handleCodeListPageChange(codeListPagination.currentPage + 1)}
              disabled={codeListPagination.currentPage === codeListPagination.totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                显示 <span className="font-medium">{(codeListPagination.currentPage - 1) * codeListPagination.itemsPerPage + 1}</span> 到 <span className="font-medium">{Math.min(codeListPagination.currentPage * codeListPagination.itemsPerPage, codeListPagination.totalItems)}</span> 条，共 <span className="font-medium">{codeListPagination.totalItems}</span> 条记录
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => handleCodeListPageChange(codeListPagination.currentPage - 1)}
                  disabled={codeListPagination.currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  上一页
                </button>
                <span className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                  {codeListPagination.currentPage} / {codeListPagination.totalPages}
                </span>
                <button
                  onClick={() => handleCodeListPageChange(codeListPagination.currentPage + 1)}
                  disabled={codeListPagination.currentPage === codeListPagination.totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  下一页
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // 确认对话框组件
  const renderConfirmDialog = () => {
    if (!showConfirmDialog || !confirmAction) return null;

    return (
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

          <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

          <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                  <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
                </div>
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    {confirmAction.title}
                  </h3>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">
                      {confirmAction.message}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="button"
                onClick={() => {
                  // 先关闭弹窗，再执行确认操作
                  setShowConfirmDialog(false);
                  setConfirmAction(null);
                  // 延迟执行确认操作，确保弹窗完全关闭后再显示Toast
                  setTimeout(() => {
                    confirmAction.onConfirm();
                  }, 100);
                }}
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
              >
                确认
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowConfirmDialog(false);
                  setConfirmAction(null);
                }}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 查看对话框组件
  const renderViewDialog = () => {
    if (!showViewDialog || !viewingCode) return null;

    return (
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

          <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

          <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    兑换码详情
                  </h3>
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">兑换码</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingCode.code}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">状态</label>
                        <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          viewingCode.status === 'unused' ? 'bg-green-100 text-green-800' :
                          viewingCode.status === 'used' ? 'bg-blue-100 text-blue-800' :
                          viewingCode.status === 'expired' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {viewingCode.status === 'unused' ? '未使用' :
                           viewingCode.status === 'used' ? '已使用' :
                           viewingCode.status === 'expired' ? '已过期' : '已禁用'}
                        </span>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">奖励类型</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {viewingCode.benefitType === 'POINTS' ? '面巾余额' :
                           viewingCode.benefitType === 'MOCK_INTERVIEW' ? '模拟面试次数' : '正式面试次数'}
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">奖励数量</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingCode.benefitAmount}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">使用情况</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingCode.usageCount}/{viewingCode.usageLimit}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">有效期</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingCode.expiresAt || '永久有效'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">创建时间</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingCode.createdAt}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">更新时间</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingCode.updatedAt}</p>
                      </div>
                    </div>
                    {viewingCode.description && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700">描述</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingCode.description}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="button"
                onClick={() => {
                  setShowViewDialog(false);
                  setViewingCode(null);
                }}
                className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:w-auto sm:text-sm"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 编辑对话框组件
  const renderEditDialog = () => {
    if (!showEditDialog || !editingCode) return null;

    return (
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

          <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

          <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    编辑兑换码
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">兑换码</label>
                      <p className="mt-1 text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{editingCode.code}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">描述</label>
                      <textarea
                        value={editingCode.description || ''}
                        onChange={(e) => setEditingCode(prev => prev ? ({ ...prev, description: e.target.value }) : null)}
                        rows={3}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="请输入兑换码描述..."
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">使用次数限制</label>
                      <input
                        type="number"
                        min="1"
                        value={editingCode.usageLimit || 1}
                        onChange={(e) => setEditingCode(prev => prev ? ({ ...prev, usageLimit: parseInt(e.target.value) }) : null)}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">有效期</label>
                      <input
                        type="datetime-local"
                        value={editingCode.expiresAt ? new Date(editingCode.expiresAt).toISOString().slice(0, 16) : ''}
                        onChange={(e) => setEditingCode(prev => prev ? ({ ...prev, expiresAt: e.target.value ? new Date(e.target.value).toISOString() : null }) : null)}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="button"
                onClick={handleSaveEdit}
                disabled={loading}
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
              >
                保存
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowEditDialog(false);
                  setEditingCode(null);
                }}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderUsageRecords = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">使用记录</h1>
        <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
          导出记录
        </button>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <input
              type="text"
              placeholder="搜索兑换码或用户..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">全部类型</option>
              <option value="POINTS">面巾余额</option>
              <option value="MOCK_INTERVIEW">模拟面试次数</option>
              <option value="FORMAL_INTERVIEW">正式面试次数</option>
            </select>
          </div>
          <div>
            <input
              type="date"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <button className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
              搜索
            </button>
          </div>
        </div>
      </div>

      {/* 使用记录表格 */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">兑换码</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用用户</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户邮箱</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖励类型</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖励数量</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用时间</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {/* 模拟数据 */}
            {[
              {
                id: 1,
                code: 'WELCOME002',
                userId: 'user123',
                userEmail: '<EMAIL>',
                benefitType: 'MOCK_INTERVIEW',
                benefitAmount: 5,
                usedAt: '2024-12-19 10:30:00'
              },
              {
                id: 2,
                code: 'VIP2024005',
                userId: 'user456',
                userEmail: '<EMAIL>',
                benefitType: 'POINTS',
                benefitAmount: 100,
                usedAt: '2024-12-18 15:45:00'
              },
              {
                id: 3,
                code: 'FORMAL007',
                userId: 'user789',
                userEmail: '<EMAIL>',
                benefitType: 'FORMAL_INTERVIEW',
                benefitAmount: 2,
                usedAt: '2024-12-17 09:20:00'
              }
            ].map((record) => (
              <tr key={record.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {record.code}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {record.userId}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {record.userEmail}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {record.benefitType === 'POINTS' ? '面巾余额' :
                   record.benefitType === 'MOCK_INTERVIEW' ? '模拟面试次数' : '正式面试次数'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {record.benefitAmount}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {record.usedAt}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button className="text-blue-600 hover:text-blue-900">查看详情</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 分页 */}
      {usageRecordsPagination.totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => handleUsageRecordsPageChange(usageRecordsPagination.currentPage - 1)}
              disabled={usageRecordsPagination.currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <button
              onClick={() => handleUsageRecordsPageChange(usageRecordsPagination.currentPage + 1)}
              disabled={usageRecordsPagination.currentPage === usageRecordsPagination.totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                显示 <span className="font-medium">{(usageRecordsPagination.currentPage - 1) * usageRecordsPagination.itemsPerPage + 1}</span> 到 <span className="font-medium">{Math.min(usageRecordsPagination.currentPage * usageRecordsPagination.itemsPerPage, usageRecordsPagination.totalItems)}</span> 条，共 <span className="font-medium">{usageRecordsPagination.totalItems}</span> 条记录
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => handleUsageRecordsPageChange(usageRecordsPagination.currentPage - 1)}
                  disabled={usageRecordsPagination.currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  上一页
                </button>
                <span className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                  {usageRecordsPagination.currentPage} / {usageRecordsPagination.totalPages}
                </span>
                <button
                  onClick={() => handleUsageRecordsPageChange(usageRecordsPagination.currentPage + 1)}
                  disabled={usageRecordsPagination.currentPage === usageRecordsPagination.totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  下一页
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderRedemptionStats = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">统计报表</h1>
        <div className="flex space-x-3">
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
            导出报表
          </button>
          <select className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="7">最近7天</option>
            <option value="30">最近30天</option>
            <option value="90">最近90天</option>
          </select>
        </div>
      </div>

      {/* 统计概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TicketIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">总生成数</p>
              <p className="text-2xl font-bold text-gray-900">1,234</p>
              <p className="text-sm text-green-600">+56 今日新增</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">已使用数</p>
              <p className="text-2xl font-bold text-gray-900">856</p>
              <p className="text-sm text-green-600">使用率 69.4%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CurrencyDollarIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">奖励发放</p>
              <p className="text-2xl font-bold text-gray-900">¥45,600</p>
              <p className="text-sm text-green-600">等值奖励</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UsersIcon className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">参与用户</p>
              <p className="text-2xl font-bold text-gray-900">423</p>
              <p className="text-sm text-green-600">活跃用户</p>
            </div>
          </div>
        </div>
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-3">兑换码生成趋势</h3>
          <ResponsiveContainer width="100%" height={200}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="users" stroke="#3B82F6" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-3">使用率统计</h3>
          <ResponsiveContainer width="100%" height={200}>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="revenue" fill="#10B981" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* 奖励类型分布 */}
      <div className="bg-white rounded-lg shadow p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-3">奖励类型分布</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 border rounded-lg">
            <div className="text-2xl font-bold text-blue-600">45%</div>
            <div className="text-sm text-gray-600">面巾余额</div>
            <div className="text-xs text-gray-500">556个兑换码</div>
          </div>
          <div className="text-center p-4 border rounded-lg">
            <div className="text-2xl font-bold text-green-600">35%</div>
            <div className="text-sm text-gray-600">模拟面试次数</div>
            <div className="text-xs text-gray-500">432个兑换码</div>
          </div>
          <div className="text-center p-4 border rounded-lg">
            <div className="text-2xl font-bold text-purple-600">20%</div>
            <div className="text-sm text-gray-600">正式面试次数</div>
            <div className="text-xs text-gray-500">246个兑换码</div>
          </div>
        </div>
      </div>

      {/* 热门兑换码排行 */}
      <div className="bg-white rounded-lg shadow p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-3">热门兑换码排行</h3>
        <div className="space-y-3">
          {[
            { rank: 1, prefix: 'WELCOME', count: 156, type: '模拟面试次数' },
            { rank: 2, prefix: 'VIP2024', count: 134, type: '面巾余额' },
            { rank: 3, prefix: 'FORMAL', count: 98, type: '正式面试次数' },
            { rank: 4, prefix: 'NEWUSER', count: 87, type: '面巾余额' },
            { rank: 5, prefix: 'BONUS', count: 76, type: '模拟面试次数' }
          ].map((item) => (
            <div key={item.rank} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${
                  item.rank === 1 ? 'bg-yellow-500' :
                  item.rank === 2 ? 'bg-gray-400' :
                  item.rank === 3 ? 'bg-orange-500' : 'bg-blue-500'
                }`}>
                  {item.rank}
                </div>
                <div className="ml-3">
                  <div className="font-medium text-gray-900">{item.prefix}</div>
                  <div className="text-sm text-gray-500">{item.type}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium text-gray-900">{item.count}</div>
                <div className="text-sm text-gray-500">使用次数</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderNotificationManagement = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">通知管理</h1>
        <button
          onClick={() => setShowCreateNotificationDialog(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          创建通知
        </button>
      </div>

      {/* 筛选器 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">全部状态</option>
              <option value="DRAFT">草稿</option>
              <option value="PUBLISHED">已发布</option>
              <option value="EXPIRED">已过期</option>
              <option value="ARCHIVED">已归档</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">类型</label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">全部类型</option>
              <option value="ANNOUNCEMENT">公告</option>
              <option value="SYSTEM_UPDATE">系统更新</option>
              <option value="MAINTENANCE">维护通知</option>
              <option value="PROMOTION">促销活动</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">优先级</label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">全部优先级</option>
              <option value="LOW">低</option>
              <option value="NORMAL">普通</option>
              <option value="HIGH">高</option>
              <option value="URGENT">紧急</option>
            </select>
          </div>
          <div>
            <button className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors mt-6">
              搜索
            </button>
          </div>
        </div>
      </div>

      {/* 通知列表 */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">优先级</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标用户</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {/* 真实数据 */}
            {notifications.map((notification: any) => (
              <tr key={notification.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{notification.title}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    notification.type === 'ANNOUNCEMENT' ? 'bg-blue-100 text-blue-800' :
                    notification.type === 'SYSTEM_UPDATE' ? 'bg-green-100 text-green-800' :
                    notification.type === 'MAINTENANCE' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-purple-100 text-purple-800'
                  }`}>
                    {notification.type === 'ANNOUNCEMENT' ? '公告' :
                     notification.type === 'SYSTEM_UPDATE' ? '系统更新' :
                     notification.type === 'MAINTENANCE' ? '维护通知' : '促销活动'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    notification.priority === 'LOW' ? 'bg-gray-100 text-gray-800' :
                    notification.priority === 'NORMAL' ? 'bg-blue-100 text-blue-800' :
                    notification.priority === 'HIGH' ? 'bg-orange-100 text-orange-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {notification.priority === 'LOW' ? '低' :
                     notification.priority === 'NORMAL' ? '普通' :
                     notification.priority === 'HIGH' ? '高' : '紧急'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    notification.status === 'DRAFT' ? 'bg-gray-100 text-gray-800' :
                    notification.status === 'PUBLISHED' ? 'bg-green-100 text-green-800' :
                    notification.status === 'EXPIRED' ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {notification.status === 'DRAFT' ? '草稿' :
                     notification.status === 'PUBLISHED' ? '已发布' :
                     notification.status === 'EXPIRED' ? '已过期' : '已归档'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {notification.targets?.[0]?.targetType === 'ALL_USERS' ? '所有用户' :
                   notification.targets?.[0]?.targetType === 'NEW_USERS' ? '新用户' : '指定用户'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {new Date(notification.createdAt).toLocaleString('zh-CN')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button className="text-blue-600 hover:text-blue-900">编辑</button>
                  {notification.status === 'DRAFT' && (
                    <button className="text-green-600 hover:text-green-900">发布</button>
                  )}
                  <button className="text-red-600 hover:text-red-900">删除</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  // 创建通知对话框
  const renderCreateNotificationDialog = () => {
    if (!showCreateNotificationDialog) return null;

    return (
      <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
          <div className="mt-3">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">创建通知</h3>
              <button
                onClick={() => setShowCreateNotificationDialog(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <form className="space-y-4" onSubmit={handleCreateNotification}>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">标题 *</label>
                <input
                  type="text"
                  value={notificationForm.title}
                  onChange={(e) => setNotificationForm({...notificationForm, title: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入通知标题"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">内容 *</label>
                <textarea
                  value={notificationForm.content}
                  onChange={(e) => setNotificationForm({...notificationForm, content: e.target.value})}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入通知内容"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">类型</label>
                  <select
                    value={notificationForm.type}
                    onChange={(e) => setNotificationForm({...notificationForm, type: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="ANNOUNCEMENT">公告</option>
                    <option value="SYSTEM_UPDATE">系统更新</option>
                    <option value="MAINTENANCE">维护通知</option>
                    <option value="PROMOTION">促销活动</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">优先级</label>
                  <select
                    value={notificationForm.priority}
                    onChange={(e) => setNotificationForm({...notificationForm, priority: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="LOW">低</option>
                    <option value="NORMAL">普通</option>
                    <option value="HIGH">高</option>
                    <option value="URGENT">紧急</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">目标用户</label>
                <select
                  value={notificationForm.targetType}
                  onChange={(e) => setNotificationForm({...notificationForm, targetType: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="ALL_USERS">所有用户</option>
                  <option value="NEW_USERS">新用户（最近7天注册）</option>
                  <option value="DATE_RANGE">按注册日期筛选</option>
                  <option value="ROLE_BASED">按用户角色筛选</option>
                </select>
              </div>

              {notificationForm.targetType === 'DATE_RANGE' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                    <input
                      type="date"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                    <input
                      type="date"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">过期时间（可选）</label>
                <input
                  type="datetime-local"
                  value={notificationForm.expiresAt}
                  onChange={(e) => setNotificationForm({...notificationForm, expiresAt: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateNotificationDialog(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  type="button"
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                >
                  保存草稿
                </button>
                <button
                  type="button"
                  onClick={handleCreateNotification}
                  disabled={loading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? '创建中...' : '创建并发布'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  };

  const renderContent = () => {
    switch (activeMenu) {
      case 'dashboard':
        return renderDashboard();
      case 'user-list':
        return renderUserList();
      case 'order-list':
        return renderOrderList();
      case 'generate-codes':
        return renderGenerateCodes();
      case 'code-list':
        return renderCodeList();
      case 'usage-records':
        return renderUsageRecords();
      case 'redemption-stats':
        return renderRedemptionStats();
      case 'notification-management':
        return renderNotificationManagement();
      default:
        return (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">功能开发中</h3>
              <p className="mt-1 text-sm text-gray-500">该功能正在开发中，敬请期待。</p>
            </div>
          </div>
        );
    }
  };

  // 如果正在检查认证状态，显示加载状态
  if (isCheckingAuth) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在验证登录状态...</p>
        </div>
      </div>
    );
  }

  // 如果未登录，显示登录页面
  if (!isLoggedIn) {
    return renderLoginPage();
  }

  return (
    <>
      {renderConfirmDialog()}
      {renderViewDialog()}
      {renderEditDialog()}
      <div className="flex h-screen bg-gray-100">
      {/* 侧边栏 */}
      <div className={`${sidebarOpen ? 'w-64' : 'w-16'} bg-white shadow-lg transition-all duration-300 ease-in-out`}>
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
          <h1 className={`font-bold text-xl text-gray-800 ${!sidebarOpen && 'hidden'}`}>
            AI面试管理系统
          </h1>
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        <nav className="mt-8">
          {menuItems.map((item) => (
            <div key={item.id} className="px-4 mb-2">
              <button
                onClick={() => {
                  if (item.children.length > 0) {
                    toggleMenuExpansion(item.id);
                  } else {
                    setActiveMenu(item.id);
                  }
                }}
                className={`w-full flex items-center justify-between px-4 py-3 text-left rounded-lg transition-colors ${
                  activeMenu === item.id
                    ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="flex items-center">
                  <item.icon className="w-5 h-5 mr-3" />
                  {sidebarOpen && <span className="font-medium">{item.name}</span>}
                </div>
                {sidebarOpen && item.children.length > 0 && (
                  expandedMenus.includes(item.id) ? (
                    <ChevronDownIcon className="w-4 h-4" />
                  ) : (
                    <ChevronRightIcon className="w-4 h-4" />
                  )
                )}
              </button>

              {sidebarOpen && item.children.length > 0 && expandedMenus.includes(item.id) && (
                <div className="ml-8 mt-2 space-y-1 transition-all duration-200 ease-in-out">
                  {item.children.map((child) => (
                    <button
                      key={child.id}
                      onClick={() => setActiveMenu(child.id)}
                      className={`w-full text-left px-4 py-2 text-sm rounded-md transition-colors ${
                        activeMenu === child.id
                          ? 'bg-blue-50 text-blue-600'
                          : 'text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      {child.name}
                    </button>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>
      </div>

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 顶部导航栏 */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-6">
            <div className="flex items-center">
              <h2 className="text-lg font-semibold text-gray-800">
                {menuItems.find(item => item.id === activeMenu)?.name ||
                 menuItems.find(item => item.children.some(child => child.id === activeMenu))?.children.find(child => child.id === activeMenu)?.name}
              </h2>
            </div>
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5v-12h5v12z" />
                </svg>
              </button>
              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    {adminUser?.name || adminUser?.email}
                  </p>
                  <p className="text-xs text-gray-500">管理员</p>
                </div>
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {(adminUser?.name || adminUser?.email)?.charAt(0).toUpperCase()}
                  </span>
                </div>
              </div>

              {/* 登出按钮 */}
              <button
                onClick={handleLogout}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
                title="登出"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </button>
            </div>
          </div>
        </header>

        {/* 主内容 */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6">
          {renderContent()}
        </main>
      </div>

      {/* 对话框 */}
      {renderCreateNotificationDialog()}
    </div>
    </>
  );
}

// 主App组件，使用ToastProvider包装
function App() {
  return (
    <ToastProvider>
      <AppContent />
    </ToastProvider>
  );
}

export default App;
